from fastapi import APIRouter, Depends
from pydantic import BaseModel
from ..core.gemini_client import complete_with_context
from ..core.rag import retrieve_context
from ..core.auth import get_current_user

router = APIRouter()

class ChatRequest(BaseModel):
    message: str
    session_id: str | None = None

class ChatResponse(BaseModel):
    reply: str
    session_id: str

@router.post("/chat", response_model=ChatResponse)
async def chat(req: ChatRequest, user=Depends(get_current_user)):
    docs = retrieve_context(req.message)
    reply, session_id = await complete_with_context(req.message, docs, req.session_id)
    return ChatResponse(reply=reply, session_id=session_id)
