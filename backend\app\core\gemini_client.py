import os, uuid
from redis import Redis
from datetime import timedelta
import google.generativeai as genai

redis = Redis(host=os.getenv("REDIS_HOST", "redis"), decode_responses=True)
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash")

async def complete_with_context(message: str, docs: list[str], session_id: str | None):
    """Generate a response using Gemini with optional context and session history."""
    if not session_id:
        session_id = str(uuid.uuid4())

    history_key = f"chat:{session_id}"
    history = redis.lrange(history_key, 0, -1)

    prompt = "\n".join(
        [*history,
         "\nContext:\n" + "\n".join(docs),
         f"User: {message}\nAI:"]
    )

    model = genai.GenerativeModel(MODEL)
    resp = await model.generate_content_async(prompt, generation_config={
        "temperature": 0.25,
        "top_p": 0.95,
        "max_output_tokens": 512
    })

    reply = resp.text.strip()
    redis.rpush(history_key, f"User: {message}", f"AI: {reply}")
    redis.expire(history_key, timedelta(hours=24))
    return reply, session_id
