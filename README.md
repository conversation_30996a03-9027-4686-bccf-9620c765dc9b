# Hospital Chatbot – Gemini Local Stack

Created: 2025-05-24T08:29:18.023974 UTC

This archive contains a minimal FastAPI backend configured to call Google Gemini
(models `gemini-pro` by default) and a Docker Compose stack that spins up:

* **api** – FastAPI service on port 8080
* **qdrant** – vector database on port 6333 (empty by default)
* **redis** – session store on port 6379

## Quick start

```bash
cp .env.sample .env    # add your GEMINI_API_KEY
docker compose up --build -d
open http://localhost:8080/docs   # interact via Swagger
```

Populate Qdrant with your PDFs via your own ingestion script or extend `core/rag.py`.

Enjoy!
